# Kritrima CLI - Installation & Troubleshooting Guide

## 🚀 Quick Installation

### Global Installation (Recommended)
```bash
npm install -g kritrima
```

### Verify Installation
```bash
kritrima-ai --help
```

## 🔧 Platform-Specific Installation

### Windows 11
```powershell
# Using PowerShell as Administrator
npm install -g kritrima
```

### WSL (Windows Subsystem for Linux)
```bash
# Inside WSL environment
npm install -g kritrima

# If you encounter Sharp errors, install with optional dependencies
npm install -g kritrima --include=optional
```

### Linux (Ubuntu/Debian)
```bash
# Standard installation
sudo npm install -g kritrima

# If <PERSON> fails to install, use without optional dependencies
sudo npm install -g kritrima --omit=optional

# Or install Sharp separately with platform-specific binaries
sudo npm install -g kritrima
npm install --os=linux --cpu=x64 sharp
```

### macOS
```bash
# Using npm
npm install -g kritrima

# Using Homebrew + npm
brew install node
npm install -g kritrima
```

## 🐛 Common Issues & Solutions

### Issue 1: Sharp Module Loading Error

**Error Message:**
```
Error: Could not load the "sharp" module using the linux-x64 runtime
```

**Solution 1 - Reinstall with Optional Dependencies:**
```bash
npm uninstall -g kritrima
npm install -g kritrima --include=optional
```

**Solution 2 - Install Sharp Separately:**
```bash
npm install --os=linux --cpu=x64 sharp
```

**Solution 3 - Skip Sharp (Image optimization disabled):**
```bash
npm install -g kritrima --omit=optional
```

**Note:** Kritrima CLI will work without Sharp, but image optimization features will be disabled with a warning message.

### Issue 2: Permission Errors (Linux/macOS)

**Error Message:**
```
EACCES: permission denied
```

**Solution:**
```bash
# Option 1: Use sudo
sudo npm install -g kritrima

# Option 2: Configure npm to use different directory
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
npm install -g kritrima
```

### Issue 3: Node.js Version Compatibility

**Error Message:**
```
engine node: wanted: >=22.0.0
```

**Solution:**
```bash
# Update Node.js to version 22 or higher
# Using nvm (recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 22
nvm use 22

# Verify version
node --version
```

### Issue 4: WSL-Specific Issues

**Problem:** CLI crashes on startup in WSL

**Solution:**
```bash
# Ensure you're using the Linux version of Node.js, not Windows
which node
# Should show /usr/bin/node or similar, not /mnt/c/...

# If using Windows Node.js, install Linux version:
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
sudo apt-get install -y nodejs

# Then reinstall Kritrima
npm install -g kritrima
```

## 🔍 Diagnostic Commands

### Check Installation
```bash
# Verify CLI is installed and working
kritrima-ai --version
kritrima-ai doctor

# Check Node.js and npm versions
node --version
npm --version

# Check global packages
npm list -g --depth=0
```

### Environment Information
```bash
# Check system information
uname -a
echo $PATH

# Check npm configuration
npm config list
```

## 📋 System Requirements

- **Node.js**: Version 22.0.0 or higher
- **npm**: Version 8.0.0 or higher
- **Operating System**: Windows 11, WSL, Linux, or macOS
- **Architecture**: x64 or arm64
- **Memory**: Minimum 512MB RAM available
- **Disk Space**: ~50MB for installation

## 🆘 Getting Help

If you continue to experience issues:

1. **Run Diagnostics:**
   ```bash
   kritrima-ai doctor
   ```

2. **Check Logs:**
   ```bash
   # Enable debug mode
   kritrima-ai --debug "test command"
   ```

3. **Report Issues:**
   - GitHub Issues: https://github.com/kritrima/kritrima/issues
   - Include output from `kritrima-ai doctor`
   - Include your system information (OS, Node.js version, etc.)

## 🔄 Updating

### Update to Latest Version
```bash
npm update -g kritrima
```

### Check for Updates
```bash
kritrima-ai update
```

## 🗑️ Uninstallation

```bash
npm uninstall -g kritrima
```

---

**Note:** The Sharp image processing library is optional. If it fails to install on your platform, Kritrima CLI will still work but image optimization features will be disabled.
