# 🔧 Installation Fix Guide - Kritrima CLI

## ⚠️ **CRITICAL: Use the Correct Package Name**

The issue you're experiencing is likely because you're installing the **wrong package name**. 

### ✅ **CORRECT Installation:**
```bash
# Uninstall any old versions first
sudo npm uninstall -g kritrima-ai
sudo npm uninstall -g kritrima

# Install the correct package
sudo npm install -g kritrima
```

### ❌ **INCORRECT (Old Package Name):**
```bash
# DON'T USE THIS - This is the old package name
sudo npm install -g kritrima-ai
```

## 🔍 **Why This Happens**

The package was renamed from `kritrima-ai` to `kritrima` for simplicity. The old package name may still exist in some caches or documentation, but it doesn't have the Sharp dependency fixes.

## 🛠️ **Complete Fix Steps**

### Step 1: Clean Installation
```bash
# Remove any existing installations
sudo npm uninstall -g kritrima-ai
sudo npm uninstall -g kritrima

# Clear npm cache
npm cache clean --force

# Install the latest version
sudo npm install -g kritrima@latest
```

### Step 2: Verify Installation
```bash
# Check the version
kritrima-ai --version

# Should show: 1.2.2 or higher
```

### Step 3: Test the CLI
```bash
# Test basic functionality
kritrima-ai --help

# This should work without any Sharp errors
```

## 🐛 **If You Still Get Sharp Errors**

If you're still getting Sharp-related errors after installing the correct package, try:

### Option 1: Force Reinstall
```bash
sudo npm uninstall -g kritrima
sudo npm cache clean --force
sudo npm install -g kritrima@1.2.2
```

### Option 2: Install with Optional Dependencies
```bash
sudo npm install -g kritrima --include=optional
```

### Option 3: Manual Sharp Installation (Optional)
```bash
# Only if you want image optimization features
sudo npm install -g sharp --unsafe-perm
sudo npm install -g kritrima
```

## 📋 **Verification Checklist**

- [ ] Uninstalled old `kritrima-ai` package
- [ ] Cleared npm cache
- [ ] Installed `kritrima` (not `kritrima-ai`)
- [ ] Version shows 1.2.2 or higher
- [ ] CLI runs without Sharp errors

## 🔧 **Version-Specific Fixes**

### Version 1.2.2 (Latest)
- ✅ Enhanced Sharp error handling
- ✅ Silent fallback for missing Sharp
- ✅ Improved cross-platform compatibility

### Version 1.2.1
- ✅ Basic Sharp optional dependency fix
- ⚠️ May still show warnings

### Version 1.2.0 and below
- ❌ Contains Sharp dependency issues
- ❌ Will crash on Linux/WSL

## 🌍 **Platform-Specific Notes**

### Linux/WSL
```bash
# Use sudo for global installation
sudo npm install -g kritrima

# If permission issues persist:
npm config set prefix ~/.local
export PATH=~/.local/bin:$PATH
npm install -g kritrima
```

### Windows
```powershell
# Run as Administrator
npm install -g kritrima
```

### macOS
```bash
# May need sudo
sudo npm install -g kritrima

# Or use homebrew node
brew install node
npm install -g kritrima
```

## 📞 **Still Having Issues?**

If you're still experiencing problems:

1. **Check your package name**: Make sure you're installing `kritrima`, not `kritrima-ai`
2. **Check your version**: Run `kritrima-ai --version` and ensure it's 1.2.2+
3. **Check installation path**: Run `which kritrima-ai` to see where it's installed
4. **Clear everything**: Uninstall all versions, clear cache, and reinstall

## 🎯 **Quick Fix Command**

```bash
# One-liner to fix everything
sudo npm uninstall -g kritrima-ai kritrima && npm cache clean --force && sudo npm install -g kritrima@latest && kritrima-ai --version
```

---

**The key is using the correct package name: `kritrima` (not `kritrima-ai`)**
